package cz.airbank.sas_agent_chstream.service.agent;

import com.fasterxml.jackson.databind.ObjectMapper;
import cz.airbank.sas.campaign.planned.call.CampaignPlannedCallEvent;
import cz.airbank.sas_agent_chstream.cache.TaskCache;
import cz.airbank.sas_agent_chstream.enumeration.AgentType;
import cz.airbank.sas_agent_chstream.kafka.producer.StoreCampaignPlannedCallKafkaProducer;
import cz.airbank.sas_agent_chstream.mapper.StoreCampaignPlannedCallEventMapper;
import cz.airbank.sas_agent_chstream.model.ci360.CampaignCallEvent;
import cz.airbank.sas_agent_chstream.repository.SasRepository;
import org.springframework.stereotype.Service;

@Service
public class CampaignPlannedCallAgent extends BaseEventProcessingAgent<
        CampaignCallEvent,
        CampaignPlannedCallEvent,
        StoreCampaignPlannedCallKafkaProducer,
        StoreCampaignPlannedCallEventMapper> {

    public CampaignPlannedCallAgent(
            StoreCampaignPlannedCallKafkaProducer producer,
            TaskCache taskCache,
            ObjectMapper objectMapper,
            SasRepository sasRepository,
            StoreCampaignPlannedCallEventMapper mapper) {
        super(producer, taskCache, objectMapper, sasRepository, mapper);
    }

    @Override
    public AgentType getAgentType() {
        return AgentType.CAMPAIGN_PLANNED_CALL;
    }

    @Override
    protected Class<CampaignCallEvent> getInputEventClass() {
        return CampaignCallEvent.class;
    }
}
