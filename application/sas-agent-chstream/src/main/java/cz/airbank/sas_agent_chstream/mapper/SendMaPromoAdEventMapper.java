package cz.airbank.sas_agent_chstream.mapper;

import cz.airbank.sas.campaign.mapromoad.v5.SendMaPromoAdEvent;
import cz.airbank.sas_agent_chstream.cache.TaskCacheEntry;
import cz.airbank.sas_agent_chstream.exception.CHStreamAgentException;
import cz.airbank.sas_agent_chstream.model.ci360.MaBannerEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class SendMaPromoAdEventMapper extends BaseMapper implements EventMapper<MaBannerEvent, SendMaPromoAdEvent> {

    @Override
    public SendMaPromoAdEvent mapToOutputEvent(MaBannerEvent inputEvent, Map<String, String> creativeContent, TaskCacheEntry taskCache, String leadId, String campaignName) {
        log.debug("Mapping data into SendMaPromoAdEvent");
        try {
            MaBannerEvent.Attributes attributes = inputEvent.attributes();
            SendMaPromoAdEvent event = new SendMaPromoAdEvent();
            event.setCuid(Long.parseLong(attributes.subjectId()));
            event.setRelatedCuid(null); //TODO
            event.setCreator("SAS360");
            event.setExternalId(leadId);
            event.setCommunicationKind(taskCache.tsk_camp_comm_type.value);
            List<String> products = new ArrayList<>();
            if (taskCache.tsk_camp_product.hasValue()) {
                String values = taskCache.tsk_camp_product.value;
                List<String> valueList = Arrays.asList(values.split(","));
                products.addAll(valueList);
            }
            event.setProducts(products);
            event.setTaskVersionId(attributes.taskVersionId());
            event.setCampaignCode(taskCache.tsk_comm_camp_name.value);
            event.setCampaignName(campaignName);
            event.setCommunicationCode(attributes.externalCode());
            event.setCommunicationName(taskCache.taskName.value);
            event.setBusinessSummaryCauseCode(taskCache.tsk_camp_buss_cause_cd.value);
            event.setDismissAfterGoAheadClick(Boolean.parseBoolean(attributes.dismissAfterGoAheadClick()));
            event.setUseDetail(parseBoolean(getValue(attributes.useDetail(), creativeContent.get("useDetail"))));

            LocalDateTime now = LocalDateTime.now();
            String formattedNow = getLocalDateTimeInIsoOffset(now);
            event.setCreated(formattedNow);

            event.setPlace(getValue(attributes.place(), creativeContent.get("place")));
            event.setSubject(getValue(attributes.subject(), creativeContent.get("subject")));
            event.setBody(getValue(attributes.body(), creativeContent.get("body")));
            event.setImage(getValue(attributes.image(), creativeContent.get("image")));
            event.setDetailTitle(getValue(attributes.detailTitle(), creativeContent.get("detailTitle")));
            event.setDetailBody(getValue(attributes.detailBody(), creativeContent.get("detailBody")));
            event.setDetailImage(getValue(attributes.detailImage(), creativeContent.get("detailImage")));
            event.setDetailTitle2(getValue(attributes.detailTitle2(), creativeContent.get("detailTitle2")));
            event.setDetailBody2(getValue(attributes.detailBody2(), creativeContent.get("detailBody2")));
            event.setDetailImage2(getValue(attributes.detailImage2(), creativeContent.get("detailImage2")));
            event.setDetailTitle3(getValue(attributes.detailTitle3(), creativeContent.get("detailTitle3")));
            event.setDetailBody3(getValue(attributes.detailBody3(), creativeContent.get("detailBody3")));
            event.setDetailImage3(getValue(attributes.detailImage3(), creativeContent.get("detailImage3")));
            event.setDetailTitle4(getValue(attributes.detailTitle4(), creativeContent.get("detailTitle4")));
            event.setDetailBody4(getValue(attributes.detailBody4(), creativeContent.get("detailBody4")));
            event.setDetailImage4(getValue(attributes.detailImage4(), creativeContent.get("detailImage4")));
            event.setDetailTitle5(getValue(attributes.detailTitle5(), creativeContent.get("detailTitle5")));
            event.setDetailBody5(getValue(attributes.detailBody5(), creativeContent.get("detailBody5")));
            event.setDetailImage5(getValue(attributes.detailImage5(), creativeContent.get("detailImage5")));
            event.setDetailTitle6(getValue(attributes.detailTitle6(), creativeContent.get("detailTitle6")));
            event.setDetailBody6(getValue(attributes.detailBody6(), creativeContent.get("detailBody6")));
            event.setDetailImage6(getValue(attributes.detailImage6(), creativeContent.get("detailImage6")));
            event.setMaPromoDetailType(getValue(attributes.maPromoDetailType(), creativeContent.get("maPromoDetailType")));
            event.setMaVersionFrom(getValue(attributes.maVersionFrom(), creativeContent.get("maVersionFrom")));
            event.setMaVersionTo(getValue(attributes.maVersionTo(), creativeContent.get("maVersionTo")));

            event.setDetailVisualization(getValue(attributes.detailVisualization(), creativeContent.get("detailVisualization")));
            event.setDetailLabel(getValue(attributes.detailLabel(), creativeContent.get("detailLabel")));

            event.setValidToAbs(getLocalDate(attributes.validToAbs()));
            event.setValidToRel(parseIntOrNull(attributes.validToRel()));
            event.setValidFromAbs(getLocalDateTimeInIsoOffset(attributes.validFromAbs()));
            event.setValidFromRel(parseIntOrNull(attributes.validFromRel()));

            event.setPrimaryUrl(getValue(attributes.primaryUrl(), creativeContent.get("primaryUrl")));
            event.setPrimaryClickMeaning(getValue(attributes.primaryClickMeaning(), creativeContent.get("primaryClickMeaning")));
            event.setPrimaryDeepLink(getValue(attributes.primaryDeepLink(), creativeContent.get("primaryDeepLink")));
            event.setPrimaryLabel(getValue(attributes.primaryLabel(), creativeContent.get("primaryLabel")));
            event.setPrimaryVisualization(getValue(attributes.primaryVisualization(), creativeContent.get("primaryVisualization")));
            event.setSecondaryUrl(getValue(attributes.secondaryUrl(), creativeContent.get("secondaryUrl")));
            event.setSecondaryClickMeaning(getValue(attributes.secondaryClickMeaning(), creativeContent.get("secondaryClickMeaning")));
            event.setSecondaryDeepLink(getValue(attributes.secondaryDeepLink(), creativeContent.get("secondaryDeepLink")));
            event.setSecondaryLabel(getValue(attributes.secondaryLabel(), creativeContent.get("secondaryLabel")));
            event.setSecondaryVisualization(getValue(attributes.secondaryVisualization(), creativeContent.get("secondaryVisualization")));
            event.setUrlParameter1(getValue(attributes.urlParameter1(), creativeContent.get("urlParameter1")));
            event.setDisplayGroup(attributes.displayGroup());
            event.setPriority(Integer.parseInt(attributes.priority()));
            event.setShowGuaranteedDays(parseIntOrNull(attributes.showGuaranteedDays()));
            event.setShowMaxCountAfterGuaranteedDays(parseIntOrNull(attributes.showMaxCountAfterGuaranteedDays()));
            event.setBusinessService(attributes.businessService());
            event.setColour(attributes.colour());
            event.setLabel(attributes.label());

            log.debug("Successfully mapped data into SendMaPromoAdEvent");
            return event;
        } catch (Exception e) {
            log.error("Failed to map data into SendMaPromoAdEvent", e);
            throw new CHStreamAgentException("ErrorCode:CH_02 - Validation error - failed mapping to SendMaPromoAdEvent", e);
        }
    }
}
