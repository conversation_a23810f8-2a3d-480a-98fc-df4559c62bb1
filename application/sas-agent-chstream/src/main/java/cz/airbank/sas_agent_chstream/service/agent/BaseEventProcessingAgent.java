package cz.airbank.sas_agent_chstream.service.agent;

import com.fasterxml.jackson.databind.ObjectMapper;
import cz.airbank.sas_agent_chstream.cache.TaskCache;
import cz.airbank.sas_agent_chstream.cache.TaskCacheEntry;
import cz.airbank.sas_agent_chstream.exception.CHStreamAgentException;
import cz.airbank.sas_agent_chstream.kafka.producer.BaseEventKafkaProducer;
import cz.airbank.sas_agent_chstream.mapper.EventMapper;
import cz.airbank.sas_agent_chstream.model.ci360.CI360Event;
import cz.airbank.sas_agent_chstream.model.ci360.EventAttributes;
import cz.airbank.sas_agent_chstream.repository.SasRepository;
import cz.airbank.sas_agent_chstream.service.Agent;
import cz.airbank.sas_agent_chstream.validator.AvroValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.util.Map;

import static cz.airbank.sas_agent_chstream.util.CreativeContentUtil.parseCreativeContent;

/**
 * Generic base agent that handles the common event processing pattern.
 *
 * @param <I> Input event type that implements CI360Event
 * @param <O> Output event type (generated from avro format)
 * @param <P> Producer type
 * @param <M> Mapper type
 */
@Slf4j
@RequiredArgsConstructor
public abstract class BaseEventProcessingAgent<
        I extends CI360Event<? extends EventAttributes>,
        O,
        P extends BaseEventKafkaProducer<O>,
        M extends EventMapper<I, O>
    > implements Agent {

    protected final P producer;
    protected final TaskCache taskCache;
    protected final ObjectMapper objectMapper;
    protected final SasRepository sasRepository;
    protected final M mapper;

    @Override
    public void processMessage(String event) {
        I inputEvent;
        try {
            inputEvent = objectMapper.readValue(event, getInputEventClass());
        } catch (Exception e) {
            log.error("Failed to parse event json into {} object", getInputEventClass().getSimpleName(), e);
            return;
        }

        EventAttributes attributes = inputEvent.attributes();
        String taskId = attributes.taskId();
        String taskVersionId = attributes.taskVersionId();

        TaskCacheEntry taskCacheEntry = taskCache.getTask(taskVersionId, taskId);
        if (taskCacheEntry == null) {
            log.warn("Failed to retrieve task cache for taskVersionId {} and taskId {}. Stop processing event", taskVersionId, taskId);
            return;
        }

        Map<String, String> creativeContent = parseCreativeContent(attributes.creativeContent());
        String campaignName = sasRepository.getCampaignName(taskCacheEntry.tsk_comm_camp_name.value);
        String leadId = getLeadId(
                attributes.datahubId(),
                attributes.responseTrackingCode(),
                attributes.timestamp());

        O outputEvent;
        try {
            outputEvent = mapper.mapToOutputEvent(inputEvent, creativeContent, taskCacheEntry, leadId, campaignName);
            AvroValidator.validateNonNullableFields(outputEvent);
        } catch (CHStreamAgentException e) {
            sasRepository.storeErrorToDb(leadId, taskCacheEntry.tsk_comm_chan_code.value, attributes.timestamp(), e.getMessage());
            return;
        }

        producer.publish(Long.parseLong(attributes.subjectId()), outputEvent);
    }

    @Override
    public void processMessage(JSONObject event) throws Exception {
        throw new UnsupportedOperationException("Use processMessage(String event)");
    }

    protected abstract Class<I> getInputEventClass();
}
