package cz.airbank.sas_agent_chstream.service.agent;

import com.fasterxml.jackson.databind.ObjectMapper;
import cz.airbank.sas.campaign.ibpromoad.v3.SendIbPromoAdEvent;
import cz.airbank.sas_agent_chstream.cache.TaskCache;
import cz.airbank.sas_agent_chstream.enumeration.AgentType;
import cz.airbank.sas_agent_chstream.kafka.producer.SendIbPromoAdEventKafkaProducer;
import cz.airbank.sas_agent_chstream.mapper.SendIbPromoAdEventMapper;
import cz.airbank.sas_agent_chstream.model.ci360.BannerEvent;
import cz.airbank.sas_agent_chstream.repository.SasRepository;
import org.springframework.stereotype.Service;

@Service
public class IbPromoAdAgent extends BaseEventProcessingAgent<
        BannerEvent,
        SendIbPromoAdEvent,
        SendIbPromoAdEventKafkaProducer,
        SendIbPromoAdEventMapper> {

    public IbPromoAdAgent(
            SendIbPromoAdEventKafkaProducer producer,
            TaskCache taskCache,
            ObjectMapper objectMapper,
            SasRepository sasRepository,
            SendIbPromoAdEventMapper mapper) {
        super(producer, taskCache, objectMapper, sasRepository, mapper);
    }

    @Override
    public AgentType getAgentType() {
        return AgentType.IB_PROMO_AD;
    }

    @Override
    protected Class<BannerEvent> getInputEventClass() {
        return BannerEvent.class;
    }
}
