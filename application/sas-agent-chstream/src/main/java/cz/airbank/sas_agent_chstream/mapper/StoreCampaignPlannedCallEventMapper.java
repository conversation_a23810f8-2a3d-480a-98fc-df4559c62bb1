package cz.airbank.sas_agent_chstream.mapper;

import cz.airbank.sas.campaign.planned.call.CampaignPlannedCallEvent;
import cz.airbank.sas_agent_chstream.cache.TaskCacheEntry;
import cz.airbank.sas_agent_chstream.exception.CHStreamAgentException;
import cz.airbank.sas_agent_chstream.model.ci360.CampaignCallEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class StoreCampaignPlannedCallEventMapper extends BaseMapper implements EventMapper<CampaignCallEvent, CampaignPlannedCallEvent> {

    @Override
    public CampaignPlannedCallEvent mapToOutputEvent(CampaignCallEvent inputEvent, Map<String, String> creativeContent, TaskCacheEntry taskCache, String leadId, String campaignName) {
        log.debug("Mapping data into StoreCampaignPlannedCallEvent");
        try {
            CampaignCallEvent.Attributes attributes = inputEvent.attributes();
            CampaignPlannedCallEvent event = new CampaignPlannedCallEvent();
            event.setCuid(Long.parseLong(attributes.subjectId()));
            event.setRelatedCuid(null); //TODO
            event.setCreator("SAS360");
            event.setExternalId(leadId);
            event.setCommunicationKind(taskCache.tsk_camp_comm_type.value);
            List<String> products = new ArrayList<>();
            if (taskCache.tsk_camp_product.hasValue()) {
                String values = taskCache.tsk_camp_product.value;
                List<String> valueList = Arrays.asList(values.split(","));
                products.addAll(valueList);
            }
            event.setProducts(products);
            event.setTaskVersionId(attributes.taskVersionId());
            event.setCampaignCode(taskCache.tsk_comm_camp_name.value);
            event.setCampaignName(campaignName);
            event.setCommunicationCode(attributes.externalCode());
            event.setCommunicationName(taskCache.taskName.value);
            event.setBusinessSummaryCauseCode(taskCache.tsk_camp_buss_cause_cd.value);

            LocalDateTime now = LocalDateTime.now();
            String formattedNow = getLocalDateTimeInIsoOffset(now);
            event.setCreated(formattedNow);

            event.setSubject(getValue(attributes.subject(), creativeContent.get("subject")));
            event.setMessage(getValue(attributes.message(), creativeContent.get("message")));
            event.setCampaignCommunicationType(attributes.campaignCommunicationType());
            event.setBusinessKey(attributes.businessKey());

            event.setValidToAbs(getLocalDate(attributes.validToAbs()));
            try {
                event.setValidToRel(Integer.parseInt(attributes.validToRel()));
            } catch (Exception e) {
                event.setValidToRel(null);
            }
            event.setValidFromAbs(getLocalDateTimeInIsoOffset(attributes.validFromAbs()));
            try {
                event.setValidFromRel(Integer.parseInt(attributes.validFromRel()));
            } catch (Exception e) {
                event.setValidFromRel(null);
            }

            log.debug("Successfully mapped data into StoreCampaignPlannedCallEvent");
            return event;
        } catch (Exception e) {
            log.error("Failed to map data into StoreCampaignPlannedCallEvent", e);
            throw new CHStreamAgentException("ErrorCode:CH_02 - Validation error - failed mapping to StoreCampaignPlannedCallEvent", e);
        }
    }
}
