package cz.airbank.sas_agent_chstream.kafka.producer;

import cz.airbank.sas.campaign.mapromopushdeeplink.v1.SendMaPromoPushDeeplinkEvent;
import cz.airbank.sas_agent_chstream.config.ApplicationConfig;
import cz.airbank.sas_agent_chstream.enumeration.AgentType;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

@Component
public class SendMaPushDeeplinkEventKafkaProducer extends BaseEventKafkaProducer<SendMaPromoPushDeeplinkEvent> {

    public SendMaPushDeeplinkEventKafkaProducer(KafkaTemplate<Long, Object> kafkaTemplate, ApplicationConfig applicationConfig) {
        super(kafkaTemplate, applicationConfig, AgentType.MA_PUSH_DEEPLINK);
    }
}
