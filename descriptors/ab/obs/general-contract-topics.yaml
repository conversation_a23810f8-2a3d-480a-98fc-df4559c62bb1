topics:
  - name: cz.airbank.obs.generalcontract.customerrelations.change.v1
    description:
      brief: "Topic se změnami stavu navázaných osob ke kontraktu"
      url: "https://wiki.airbank.cz/x/eQRrG"
    partitions: 10
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: long
    schema:
      - name: GeneralContractCustomerRelationsChangeEvent
        artifactId: cz.airbank.obs.generalcontract.customerrelations.GeneralContractCustomerRelationsChangeEvent
        groupId: default
        description: Schema for customer relations change events
        version: 1
        schemaRef: schemas/general-contract-customer-relations-change-event.avsc
    acl:
      read:
        - principal: "User:SAS_KAFKA_USER"
          name: sas
          group: sas-agent-kafka
          generateDlt: true
          description:
            brief: "inbound events"
      write:
        - principal: "User:OBS_KAFKA_USER"
          name: obs
          description:
            brief: "outbound events"
  - name: cz.airbank.obs.generalcontract.statuschanged.v1
    description:
      brief: "Topic se změnami stavu smluv"
      url: "https://wiki.airbank.cz/display/SA/GeneralContractStatusChanged"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: long
    schema:
      - name: GeneralContractStatusChangedEvent
        artifactId: cz.airbank.obs.generalcontract.statuschanged.v1.GeneralContractStatusChangedEvent
        groupId: default
        description: Schema for customer status changed events
        version: 1
        schemaRef: schemas/general-contract-status-changed-event.avsc
    acl:
      read:
        - principal: "User:BENMAN_KAFKA_USER"
          name: benman
          group: benman
          generateDlt: true
          description:
            brief: "inbound events"
        - principal: "User:FES_KAFKA_USER"
          name: fes
          group: fes
          generateDlt: true
          description:
            brief: "inbound events"
        - principal: "User:CTC_KAFKA_USER"
          name: ctc
          group: ctc
          generateDlt: true
          description:
            brief: "inbound events"
      write:
        - principal: "User:OBS_KAFKA_USER"
          name: obs
          description:
            brief: "outbound events"
