<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation=" http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cz.airbank.common</groupId>
        <artifactId>superpom</artifactId>
        <version>10.0.7</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>cz.airbank.kafka.provisioning</groupId>
    <artifactId>kafka-provisioning</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <repositories>
        <repository>
            <id>nexus-public</id>
            <url>https://nexus.abank.cz/content/groups/public</url>
        </repository>
        <repository>
            <id>nexus-releases</id>
            <url>https://nexus.abank.cz/content/repositories/releases</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>nexus-snapshots</id>
            <url>https://nexus.abank.cz/content/repositories/snapshots</url>
            <releases>
                <enabled>false</enabled>
            </releases>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>nexus-public</id>
            <url>https://nexus.abank.cz/content/groups/public</url>
        </pluginRepository>
    </pluginRepositories>

    <modules>
        <module>application</module>
    </modules>

    <properties>
        <revision>ave_XR-14365-SNAPSHOT</revision>
    </properties>


</project>
